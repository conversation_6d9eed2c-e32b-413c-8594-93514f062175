import request from '~/api/request';
Component({
  properties: {
    // Whether the overlay is visible
    visible: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // Store the token status
    isLoggedIn: false,
    // 登录相关
    resolvePrivacyAuthorization: null
  },

  lifetimes: {
    attached() {
      console.log('login-overlay 组件 attached');
      // Check login status when component is attached
      this.checkLoginStatus();

      // 监听隐私授权事件
      //this.setupPrivacyListener();
    },
    ready() {
      console.log('login-overlay 组件 ready');
      // 组件准备好后，再次检查登录状态
      this.checkLoginStatus();
    },
    detached() {
      // 取消监听隐私授权事件
      wx.offNeedPrivacyAuthorization();
    }
  },

  pageLifetimes: {
    show() {
      console.log('login-overlay 组件页面 show');
      // Check login status when page is shown
      this.checkLoginStatus();
    }
  },

  methods: {
    // Check if user is logged in
    checkLoginStatus() {
      const token = wx.getStorageSync('access_token');
      console.log('login-overlay 检查登录状态, token:', token ? '存在' : '不存在');
      this.setData({
        isLoggedIn: !!token
      });
    },

    // Handle overlay click
    handleOverlayClick(e) {
      this.triggerEvent('overlayclick', { visible: false });
    },

    //登录
    login(e) {
      // this.getPhoneNumber(e)
      wx.login({
        success: (resa) => {
          wx.getUserInfo({
            success: (resb) => {
              const payload = {
                appid: 'wxa5f7c5d4b5c1d034',
                code: resa.code,
                rawData: resb.rawData,
                signature: resb.signature,
                encryptedData: resb.encryptedData,
                iv: resb.iv,
                phoneNumber: e.detail.code,
              }
              request('/wechat/user/auth', 'post', payload).then(rescc => {
                console.log('access_token:', rescc.data.data);
                if (rescc.data && rescc.data.data && (rescc.data.data.token != '')) {
                  wx.setStorageSync('access_token', rescc.data.data.token)
                  this.setData({
                    isLoggedIn: true
                  });
                  this.triggerEvent('customevent', { success: true });
                }
              })
            }
          })
        }
      })
    },

    loginMock() {
      // 先检查隐私授权状态
      if (this.data.needPrivacyAuthorization) {
        this.setData({
          showPrivacyPopup: true
        });
        return;
      }
      const token = "eyJ0eXAiOiJKc29uV2ViVG9rZW4iLCJhbGciOiJIUzI1NiJ9.eyJVdWlkIjoiNTUyNjZlNDgtMDc3OS00ZjdhLThjOGItYmY5ZDAxODQ4MDRlIiwiVXNlcklkIjoiMyIsIlBob25lIjoiMTU2NTgwMjIxODMiLCJpYXQiOjE3NDc5NjI5MzEsIm5iZiI6MTc0Nzk2MjkzMSwiZXhwIjoxNzQ4NTY3NzMxfQ.5YA12nHrvRyFemKbxutgMRo4X9WO2QJmD0_aOPPiJ58"
      wx.setStorageSync('access_token', token)
      // 更新登录状态，这会自动关闭弹窗
      console.log('模拟登录成功，更新 isLoggedIn 状态为 true');
      this.setData({
        isLoggedIn: true
      }, () => {
        console.log('isLoggedIn 状态已更新为:', this.data.isLoggedIn);
      });
      // 触发登录成功事件，传递登录成功的标志
      console.log('模拟登录成功事件');
      this.triggerEvent('customevent', { success: true });

      // 手动触发 overlayclick 事件，确保弹窗关闭
      this.triggerEvent('overlayclick', { visible: false });
    }
  }
})