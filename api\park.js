import request from './request';

/* apiGetNearbyParks 响应数据结构示例
  {
    "code": 0,
    "data": {
      "curPage": "string",
      "pages": "string",
      "results": [
        {
          "id": "string",
          "parkNo": "string",
          "parkName": "string",
          "longitude": "string",
          "latitude": "string",
          "parkAddress": "string",
          "parkPhone": null,
          "parkType": {
            "code": "string",
            "desc": "string"
          },
          "availableSlots": 0,
          "distance": "string",
          "averagePrice": "string",
          "timeUnit": {
            "code": "string",
            "desc": "string"
          }
        }
      ],
      "size": "string",
      "total": "string",
      "extraData": null,
      "cursor": null
    },
    "msg": "string",
    "path": null,
    "timestamp": "string",
    "errorMsg": null
  }
*/
export const apiGetNearbyParks = async (payload) => {
  const res = await wx.getLocation({ type: 'wgs84' })
  const { latitude, longitude, speed, accuracy } = res
  console.log('定位坐标: ', latitude, longitude);
  const listRes = await request('/mini/park/nearby', 'POST', {
    model: {
      latitude: latitude,
      longitude: longitude,
      radius: 520000
    },
    current: 0,
    size: 10,
    order: 'descending'
  })
  return listRes.data
};

/* apiSearchParks 响应数据结构示例
  {
    "code": 0,
    "data": {
      "curPage": "0",
      "pages": "1",
      "results": [
        {
          "id": "1920717154796822529",
          "parkNo": "1887991720460825",
          "parkName": "拱墅区测试停车场",
          "longitude": "122.0",
          "latitude": "22.0",
          "parkAddress": "杭州市拱墅区测试街道测试停车场",
          "parkType": {
            "code": "OPEN_AIR_PARKING",
            "desc": "开放式车场"
          },
          "availableSlots": 3,
          "distance": "2061421.620489416",
          "averagePrice": "1.00",
          "timeUnit": {
            "code": "HOUSR",
            "desc": "小时"
          }
        }
      ],
      "size": "10",
      "total": "1",
      "extraData": null,
      "cursor": null
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747017246776",
    "errorMsg": null
  }
*/
export const apiSearchParks = async (payload) => {
  const { parkName, current = 1, size = 10, sort = 'id', order = 'descending' } = payload;
  const res = await wx.getLocation({ type: 'wgs84' })
  const { latitude, longitude, speed, accuracy } = res
  const searchRes = await request('/mini/park/list', 'POST', {
    current,
    size,
    model: {
      longitude,
      latitude,
      parkName
    }
  });
  return searchRes.data;
};

/* apiGetParkDetail 响应数据结构示例
  {
    "code": 0,
    "data": {
      "id": "1922843368063823874",
      "parkNo": "1885469373358532",
      "parkName": "杭州市拱墅区测试停车场",
      "longitude": "30.25961",
      "latitude": "120.13026",
      "parkAddress": "浙江省杭州市拱墅区停车场",
      "parkType": {
        "code": "CURBSIDE_PARKING",
        "desc": "路边式车场"
      },
      "availableSlots": 2,
      "distance": "937144.69143519",
      "unitCharge": "10.00",
      "averagePrice": "10.00",
      "timeUnit": {
        "code": "HOUSR",
        "desc": "小时"
      }
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747569299361",
    "errorMsg": null
  }
*/
export const apiGetParkDetail = async (payload) => {
  // 获取当前位置
  const locationRes = await wx.getLocation({ type: 'wgs84' });
  const { latitude, longitude } = locationRes;
  const { id } = payload;
  const detailRes = await request('/mini/park/query', 'POST', {
    id,
    longitude,
    latitude
  });
  return detailRes.data;
};
