import request from './request';

/* apiGetEarningsList 响应数据结构示例
  {
    "code": 0,
    "data": {
        "curPage": "0",
        "pages": "1",
        "results": [
            {
                "id": "2",
                "parkId": "1922976904607494145",
                "lotId": "1922845474623647745",
                "lockId": "3002",
                "orderId": "2",
                "parkName": "测试停车场1",
                "location": "地下一层",
                "code": "B101",
                "actualRevenue": "211.50",
                "settleTime": "2025-05-18 23:59:59"
            },
            // 更多收益记录...
        ],
        "size": "10",
        "total": "4",
        "extraData": null,
        "cursor": null
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747585800179",
    "errorMsg": null
  }
*/
export const apiGetEarningsList = async (payload) => {
  const { current = 1, size = 10, model = {} } = payload;
  const earningsRes = await request('/mini/park/earn/pageByCustomer', 'POST', {
    current,
    size,
    model
  });
  return earningsRes.data;
};

/* apiGetTotalRevenue 响应数据结构示例
  {
    "code": 0,
    "data": "238.50",
    "msg": "成功",
    "path": null,
    "timestamp": "1747586992954",
    "errorMsg": null
  }
*/
export const apiGetTotalRevenue = async () => {
  const revenueRes = await request('/mini/park/earn/totalRevenue', 'POST');
  return revenueRes.data;
};

/* apiGetEarnDetailList 响应数据结构示例
  {
    "code": 0,
    "data": [
        {
            "id": null,                                  // 收入id
            "parkId": "1922843368063823874",            // 车场id
            "lotId": "1922845176475742210",             // 车位id
            "lockId": "3001",                           // 车锁id
            "parkName": "杭州市拱墅区测试停车场",        // 车场名称
            "location": "地下一层",                      // 车位区域
            "code": "B101",                             // 车位编号
            "totalRevenue": "27.00"                     // 总收入
        },
        // 更多车位收益明细...
    ],
    "msg": "成功",
    "path": null,
    "timestamp": "1747588174367",
    "errorMsg": null
  }
*/
export const apiGetEarnDetailList = async () => {
  const earnDetailRes = await request('/mini/park/earn/getEarnList', 'POST');
  return earnDetailRes.data;
};

/* apiGetLockTotalRevenue 响应数据结构示例
  {
    "code": 0,
    "data": "238.50",                                  // 金额
    "msg": "成功",
    "path": null,
    "timestamp": "1747589061900",
    "errorMsg": null
  }
*/
export const apiGetLockTotalRevenue = async (payload) => {
  const { lockId } = payload;
  const revenueRes = await request('/mini/park/earn/totalLockRevenue', 'POST', {
    lockId
  });
  return revenueRes.data;
};

/* apiGetLockEarningsList 响应数据结构示例
  {
    "code": 0,
    "data": {
        "curPage": "0",                                // 当前页码
        "pages": "1",                                  // 总页数
        "results": [                                   // 收益记录列表
            {
                "id": "2",                             // 收益记录ID
                "parkId": "1922976904607494145",       // 车场ID
                "lotId": "1922845474623647745",        // 车位ID
                "lockId": "1922845176509296642",       // 车锁ID
                "orderId": "2",                        // 订单ID
                "parkName": "测试停车场1",              // 车场名称
                "location": "地下一层",                 // 车位区域
                "code": "B101",                        // 车位编号
                "actualRevenue": "211.50",             // 实际收益
                "settleTime": "2025-05-18 23:59:59"    // 结算时间
            },
            // 更多收益记录...
        ],
        "size": "10",                                  // 每页记录数
        "total": "2",                                  // 总记录数
        "extraData": null,                             // 额外数据
        "cursor": null                                 // 游标
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747589740296",
    "errorMsg": null
  }
*/
export const apiGetLockEarningsList = async (payload) => {
  const { lockId, current = 1, size = 10 } = payload;
  const earningsRes = await request('/mini/park/earn/pageByLock', 'POST', {
    current,
    size,
    model: {
      lockId
    }
  });
  return earningsRes.data;
};
