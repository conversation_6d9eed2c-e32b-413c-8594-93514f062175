<t-message id="t-message" />
<t-dialog id="t-dialog" />

<view class="parking-management-container {{isHeaderCollapsed ? 'header-collapsed' : ''}}">
  <!-- 固定导航栏 -->
  <gradient-navbar title="车位管理" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header
    enable-collapse="{{true}}"
    collapse-threshold="{{80}}"
    id="floatingHeader"
    custom-style="{{isHeaderCollapsed ? 'max-height: 180rpx;' : ''}}"
    custom-class="{{isHeaderCollapsed ? 'header-collapsed' : ''}}"
  >
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="header-main">
      <!-- 车位统计信息 -->
      <view class="spots-info-section">
        <view class="spots-info-tag">
          <t-icon name="parking" size="32rpx" color="#ffffff" />
          <text>目前您共有 {{totalSpots || 0}} 个共享车位</text>
        </view>
      </view>
    </view>

    <!-- 次要内容（折叠时隐藏） -->
    <view slot="secondary" class="header-secondary" style="{{isHeaderCollapsed ? 'display: none !important;' : ''}}"
          wx:if="{{!isHeaderCollapsed}}">
      <!-- 温馨提示区域 -->
      <view class="notice-section">
        <view class="notice-title">
          <t-icon name="info-circle" size="32rpx" color="#ffffff" />
          <text>温馨提示</text>
        </view>
        <view class="notice-content">
          <view class="notice-item">• 设置共享后，您将无法控制车位锁，直到共享结束</view>
          <view class="notice-item">• 停车单价建议结合停车场价格合理定义以提高使用率</view>
        </view>
      </view>
    </view>
  </floating-header>

  <!-- 车位列表内容区域 -->
  <view class="parking-list-content">
      <!-- 车位列表 -->
      <view class="parking-list">
        <block wx:if="{{loading && current === 1}}">
          <view class="loading-container">
            <t-loading theme="circular" size="40rpx" loading />
            <text class="loading-text">加载中...</text>
          </view>
        </block>
        <block wx:elif="{{parkingList.length === 0}}">
          <view class="empty-container">
            <t-empty
              icon="info-circle-filled"
              description="暂无车位信息"
              wx:if="{{!loading}}"
            />
          </view>
        </block>
        <block wx:else>
          <view class="parking-item" wx:for="{{parkingList}}" wx:key="id">
            <!-- 车位号和状态标签 -->
            <view class="spot-header">
              <view class="spot-number">{{item.spotNumber}}</view>

              <!-- 状态标签容器 -->
              <view class="status-tags">
                <!-- 使用中标签 -->
                <view class="status-tag status-using" wx:if="{{item.isUsing}}">
                  <t-icon name="user" size="24rpx" color="#0052D9" />
                  <text>使用中</text>
                </view>

                <!-- 共享中标签 -->
                <view class="status-tag status-sharing" wx:if="{{item.isSharing}}">
                  <t-icon name="share" size="24rpx" color="#07C160" />
                  <text>共享中</text>
                </view>

                <!-- 未共享标签 -->
                <view class="status-tag status-unshared" wx:if="{{!item.isSharing && !item.isUsing}}">
                  <text>未共享</text>
                </view>

                <!-- 空闲标签 - 当共享中但未被使用时显示 -->
                <view class="status-tag status-free" wx:if="{{item.isSharing && !item.isUsing}}">
                  <t-icon name="chevron-down-circle" size="24rpx" color="#07C160" />
                  <text>空闲</text>
                </view>
              </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 车位详情 -->
            <view class="spot-details">
              <!-- 地址信息 -->
              <view class="info-item">
                <view class="info-label">车位地址</view>
                <view class="info-value">{{item.address}}</view>
              </view>

              <view class="divider"></view>

              <!-- 共享时段 -->
              <view class="info-item">
                <view class="info-label">共享时段</view>
                <view class="info-value">{{item.shareTime || '未设置'}}</view>
              </view>

              <view class="divider"></view>

              <!-- 价格信息 -->
              <view class="info-item">
                <view class="info-label">共享单价</view>
                <view class="info-value">{{item.pricePerHour ? '￥' + item.pricePerHour + '元/小时' : '未设置'}}</view>
              </view>

              <view class="divider"></view>

              <view class="info-item">
                <view class="info-label">封顶价格</view>
                <view class="info-value">{{item.maxPrice ? '￥' + item.maxPrice + '元' : '未设置'}}</view>
              </view>

              <!-- 操作按钮区域 -->
              <view class="action-buttons">
                <!-- 使用中状态 - 显示联系用户按钮 -->
                <block wx:if="{{item.isUsing}}">
                  <view class="action-button contact-button" catchtap="onContactTap" data-id="{{item.id}}">
                    联系用户
                  </view>
                </block>

                <!-- 共享中但未使用状态 - 显示升锁/降锁按钮 -->
                <block wx:if="{{!item.isUsing}}">
                  <view class="action-button lock-button {{buttonCooldown.isActive ? 'button-disabled' : ''}}" catchtap="onLockOperateTap" data-id="{{item.id}}">
                    {{buttonCooldown.isActive ? buttonCooldown.remainingTime + 's' : '开锁/关锁'}}
                  </view>
                </block>

                <!-- 未共享状态 - 显示设置共享按钮 -->
                <block >
                  <view class="action-button share-button" catchtap="onShareTap" data-id="{{item.id}}">
                    设置共享
                  </view>
                </block>
              </view>
            </view>
          </view>

          <!-- 加载更多内容 -->
          <load-more
            list-is-empty="{{!parkingList.length}}"
            status="{{loadMoreStatus}}"
            no-more-text="没有更多车位了"
            bind:retry="onRetryLoad"
          />
        </block>
      </view>
  </view>

  <!-- 共享设置弹窗 -->
  <t-popup visible="{{showSharePopup}}" placement="bottom" bind:visible-change="onPopupVisibleChange">
    <view class="share-popup">
      <view class="popup-header">
        <view class="popup-title">{{selectedSpot.spotNumber}}</view>
        <view class="popup-subtitle">{{selectedSpot.address}}</view>
      </view>

      <!-- 共享时段选择 -->
      <view class="form-item">
        <view class="form-label">共享时段</view>
        <view class="time-picker-container">
          <!-- 开始时间选择器 -->
          <view class="time-picker {{startTimeError ? 'time-picker-error' : ''}}" bindtap="onStartTimeTap">
            <text>{{shareStartTime || '请选择'}}</text>
            <t-icon name="chevron-down" size="24rpx" color="{{startTimeError ? '#E34D59' : '#999999'}}" />
          </view>

          <view class="time-separator">至</view>

          <!-- 次日选择器 -->
          <view class="day-picker" bindtap="onDayTypeTap">
            <text>{{isCrossDay ? '次日' : '当日'}}</text>
            <t-icon name="chevron-down" size="24rpx" color="#999999" />
          </view>

          <!-- 结束时间选择器 -->
          <view class="time-picker {{endTimeError ? 'time-picker-error' : ''}}" bindtap="onEndTimeTap">
            <text>{{shareEndTime || '请选择'}}</text>
            <t-icon name="chevron-down" size="24rpx" color="{{endTimeError ? '#E34D59' : '#999999'}}" />
          </view>
        </view>
        <view class="time-duration" wx:if="{{shareDuration}}">
          <text>{{shareDuration}}</text>
        </view>
      </view>

      <!-- 共享单价 -->
      <view class="form-item">
        <view class="form-label">共享单价</view>
        <view class="price-input-container {{priceError ? 'price-input-error' : ''}}">
          <t-input
            value="{{sharePrice}}"
            placeholder="请输入单价"
            type="number"
            suffix="元 / 小时"
            bind:change="onPriceChange"
            status="{{priceError ? 'error' : ''}}"
          />
        </view>
      </view>

      <!-- 封顶价格 -->
      <view class="form-item">
        <view class="form-label">封顶价格</view>
        <view class="price-input-container {{maxPriceError ? 'price-input-error' : ''}}">
          <t-input
            value="{{maxPrice}}"
            placeholder="请输入封顶价格"
            type="number"
            suffix="元"
            bind:change="onMaxPriceChange"
            status="{{maxPriceError ? 'error' : ''}}"
          />
        </view>
      </view>

      <!-- 温馨提示 -->
      <view class="tips-section">
        <view class="tips-title">温馨提示：</view>
        <view class="tips-content">1.停车单价建议结合停车场价格合理定义以提高使用率</view>
        <view class="tips-content">2.开启共享期间您将无法控制车位锁</view>
      </view>

      <!-- 底部按钮 -->
      <view class="popup-buttons">
        <view class="popup-button cancel-button" bindtap="onCancelShare">
          返回
        </view>
        <view class="popup-button confirm-button" bindtap="onNextStep">
          下一步
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 确认信息弹窗 -->
  <t-popup visible="{{showConfirmPopup}}" placement="bottom" bind:visible-change="onConfirmPopupVisibleChange">
    <view class="confirm-popup">
      <view class="popup-header">
        <view class="popup-title">{{selectedSpot.spotNumber}}</view>
        <view class="popup-subtitle">{{selectedSpot.address}}</view>
        <!-- 右上角"仅保存不共享"按钮 -->
        <view class="save-only-button" bindtap="onSaveOnly">仅保存不共享</view>
      </view>

      <!-- 确认信息 -->
      <view class="confirm-info">
        <view class="confirm-item">
          <view class="confirm-label">车位地址</view>
          <view class="confirm-value">{{selectedSpot.address}}</view>
        </view>

        <view class="confirm-item">
          <view class="confirm-label">共享时段</view>
          <view class="confirm-value">{{shareStartTime}} 至 {{isCrossDay ? '次日' : ''}} {{shareEndTime}}</view>
        </view>

        <view class="confirm-item">
          <view class="confirm-label">共享单价</view>
          <view class="confirm-value">￥{{sharePrice}} 元/小时</view>
        </view>

        <view class="confirm-item">
          <view class="confirm-label">封顶价格</view>
          <view class="confirm-value">￥{{maxPrice}} 元</view>
        </view>
      </view>

      <!-- 分隔线 -->
      <view class="confirm-divider"></view>

      <!-- 底部按钮 -->
      <view class="popup-buttons">
        <view class="popup-button cancel-button" bindtap="onBackToShare">
          返回
        </view>
        <view class="popup-button confirm-button" bindtap="onStartSharing">
          保存并立刻共享
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 时间选择器 -->
  <t-date-time-picker
    title="选择时间"
    visible="{{showTimePicker}}"
    mode="{{['null', 'minute']}}"
    value="{{currentTimeType === 'start' ? shareStartTime : shareEndTime}}"
    format="HH:mm:ss"
    bindchange="onTimePickerChange"
    bindcancel="hidePicker"
    start="{{'2000-01-01 00:00:00'}}"
    end="{{'2000-01-01 '+ (currentTimeType === 'end' && isCrossDay ? shareEndTimeLimit : '23:59:00')}}"
  />
  <!-- 日期类型选择器 -->
  <t-picker
    visible="{{showDayTypePicker}}"
    title="选择日期类型"
    value="{{dayTypePickerValue}}"
    cancelBtn="取消"
    confirmBtn="确认"
    bindchange="onDayTypeChange"
  >
    <t-picker-item options="{{dayTypeOptions}}" />
  </t-picker>

  <!-- 锁操作弹窗 -->
  <t-popup
    visible="{{lockOperationPopup.visible}}"
    placement="center"
    overlay-props="{{lockOperationPopup.overlayProps}}"
    close-on-overlay-click="{{false}}"
  >
    <view class="lock-operation-popup">
      <view class="lock-operation-content">
        <!-- 加载图标和浏览图标 -->
        <view class="lock-operation-icon">
          <t-loading wx:if="{{lockOperationPopup.loading}}" theme="circular" size="60rpx" />
          <t-icon wx:else name="browse-gallery" size="60rpx" color="#0052D9" />
        </view>

        <!-- 操作文本 -->
        <view class="lock-operation-text">{{lockOperationPopup.text}}</view>

        <!-- 按钮区域 -->
        <view class="lock-operation-buttons" wx:if="{{lockOperationPopup.showRetry}}">
          <view class="lock-operation-button cancel-btn" bindtap="onCancelLockOperation">
            退出
          </view>
          <view class="lock-operation-button retry-btn {{buttonCooldown.isActive ? 'button-disabled' : ''}}" bindtap="onRetryLockOperation">
            {{buttonCooldown.isActive ? buttonCooldown.remainingTime + 's' : '重试'}}
          </view>
        </view>
      </view>
    </view>
  </t-popup>
</view>
