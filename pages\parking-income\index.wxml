<t-message id="t-message" />

<view class="parking-income-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="车位收益" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header enable-collapse="{{false}}" collapse-threshold="{{80}}" id="floatingHeader">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="income-main">
      <!-- 收益统计信息 -->
      <view class="income-stats">
        <view class="stats-item">
          <view class="stats-value">{{totalSpots || 0}}</view>
          <view class="stats-label">车位数量</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-value">{{totalOrders || 0}}</view>
          <view class="stats-label">订单数量</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-value">{{avgIncome || '0.00'}}</view>
          <view class="stats-label">平均收益</view>
        </view>
      </view>
    </view>
  </floating-header>

  <!-- 内容区域 -->
  <view class="income-content">
    <t-pull-down-refresh
      value="{{refreshing}}"
      loadingProps="{{loadingProps}}"
      loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
      bind:refresh="onRefresh"
    >
      <!-- 收益列表 -->
      <view class="income-list">
        <block wx:if="{{incomeList.length > 0}}">
          <view
            class="income-item"
            wx:for="{{incomeList}}"
            wx:key="id"
            bindtap="onItemTap"
            data-id="{{item.id}}"
          >
            <view class="item-left">
              <image class="item-icon" src="/static/income/parking_icon.png" mode="aspectFit"></image>
            </view>
            <view class="item-center">
              <view class="item-spot">{{item.spotNumber}}</view>
              <view class="item-info">
                <text class="item-location">{{item.parkingName}}</text>
                <text class="item-time">{{item.time}}</text>
              </view>
            </view>
            <view class="item-right">
              <view class="item-amount">+￥{{item.amount}}</view>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <block wx:else>
          <t-empty
            icon="info-circle-filled"
            description="暂无收益记录"
            wx:if="{{!loading}}"
          />
        </block>
      </view>

      <!-- 加载状态 -->
      <view class="loading-wrapper" wx:if="{{loading}}">
        <t-loading theme="circular" size="40rpx" text="加载中..." inherit-color />
      </view>

      <!-- 加载完成提示 -->
      <view class="no-more" wx:if="{{incomeList.length > 0 && !hasMoreData && !loading}}">
        没有更多数据了
      </view>
    </t-pull-down-refresh>
  </view>
</view>
