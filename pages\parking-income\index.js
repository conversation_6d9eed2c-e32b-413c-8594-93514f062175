import Message from 'tdesign-miniprogram/message/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 总收益
    totalIncome: '126.00',
    // 车位数量
    totalSpots: 3,
    // 订单数量
    totalOrders: 12,
    // 平均收益
    avgIncome: '10.50',
    // 收益列表
    incomeList: [
      {
        id: '1',
        spotNumber: '地下 B1-005',
        parkingName: '南北商务港',
        time: '2025-4-22 15:24:00',
        amount: '5.00'
      },
      {
        id: '2',
        spotNumber: '地下 B1-005',
        parkingName: '南北商务港',
        time: '2025-4-21 12:15:30',
        amount: '5.00'
      },
      {
        id: '3',
        spotNumber: '地下 B1-005',
        parkingName: '南北商务港',
        time: '2025-4-20 09:45:12',
        amount: '5.00'
      },
      {
        id: '4',
        spotNumber: '地下 B1-005',
        parkingName: '南北商务港',
        time: '2025-4-19 18:30:45',
        amount: '5.00'
      },
      {
        id: '5',
        spotNumber: '地下 B1-005',
        parkingName: '南北商务港',
        time: '2025-4-18 14:20:10',
        amount: '5.00'
      }
    ],
    // 是否正在加载
    loading: false,
    // 是否正在刷新
    refreshing: false,
    // 是否有更多数据
    hasMoreData: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 加载配置
    loadingProps: {
      size: '50rpx',
    },
    // 页面滚动位置
    scrollTop: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取收益数据
    this.fetchIncomeData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时，确保浮动头部状态正确
    setTimeout(() => {
      this.resetFloatingHeader();
    }, 300);
  },

  /**
   * 重置浮动头部状态
   */
  resetFloatingHeader() {
    const query = wx.createSelectorQuery();
    query.select('#floatingHeader').node();
    query.exec((res) => {
      if (res && res[0] && res[0].node) {
        const headerComponent = res[0].node.component;
        if (headerComponent && typeof headerComponent.setCollapsed === 'function') {
          // 先设置为展开状态
          headerComponent.setCollapsed(false);

          // 如果页面已经滚动，则根据滚动位置设置折叠状态
          if (this.data.scrollTop > 80) {
            headerComponent.setCollapsed(true);
          }
        }
      }
    });
  },

  /**
   * 获取收益数据
   */
  fetchIncomeData(isRefresh = false) {
    // 如果是刷新，重置页码
    if (isRefresh) {
      this.setData({
        currentPage: 1,
        hasMoreData: true,
        incomeList: []
      });
    }

    // 如果没有更多数据，直接返回
    if (!this.data.hasMoreData && !isRefresh) {
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true
    });

    // 模拟API请求
    setTimeout(() => {
      // 模拟数据
      const mockData = {
        totalIncome: '126.00',
        totalSpots: 3,
        totalOrders: 12,
        avgIncome: '10.50',
        list: this.data.incomeList,
        hasMore: this.data.currentPage < 3 // 模拟只有3页数据
      };

      // 更新数据
      this.setData({
        totalIncome: mockData.totalIncome,
        totalSpots: mockData.totalSpots,
        totalOrders: mockData.totalOrders,
        avgIncome: mockData.avgIncome,
        incomeList: isRefresh ? mockData.list : [...this.data.incomeList, ...mockData.list],
        hasMoreData: mockData.hasMore,
        currentPage: this.data.currentPage + 1,
        loading: false,
        refreshing: false
      });
    }, 1000);
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.fetchIncomeData(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.loading) {
      this.fetchIncomeData();
    }
  },

  /**
   * 页面滚动事件的处理函数
   */
  onPageScroll(e) {
    // 保存滚动位置
    this.setData({ scrollTop: e.scrollTop });
  },

  /**
   * 点击收益项
   */
  onItemTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/income-detail/index?id=${id}`
    });
  },

  /**
   * 返回按钮点击事件
   */
  onBackTap() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 显示消息提示
   */
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
